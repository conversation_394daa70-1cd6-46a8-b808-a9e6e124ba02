-- ----------------------------
-- Table structure for ls_wechat_followers
-- ----------------------------
DROP TABLE IF EXISTS `ls_wechat_followers`;
CREATE TABLE `ls_wechat_followers` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `openid` varchar(64) NOT NULL COMMENT '用户openid',
  `unionid` varchar(64) DEFAULT '' COMMENT '用户unionid',
  `nickname` varchar(100) DEFAULT '' COMMENT '用户昵称',
  `sex` tinyint(1) DEFAULT '0' COMMENT '用户性别(0=未知, 1=男, 2=女)',
  `city` varchar(50) DEFAULT '' COMMENT '用户所在城市',
  `country` varchar(50) DEFAULT '' COMMENT '用户所在国家',
  `province` varchar(50) DEFAULT '' COMMENT '用户所在省份',
  `language` varchar(20) DEFAULT '' COMMENT '用户语言',
  `headimgurl` varchar(500) DEFAULT '' COMMENT '用户头像',
  `subscribe_time` int(11) DEFAULT '0' COMMENT '关注时间',
  `subscribe` tinyint(1) DEFAULT '1' COMMENT '是否关注(0=未关注, 1=已关注)',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_openid` (`openid`) USING BTREE,
  KEY `idx_unionid` (`unionid`) USING BTREE,
  KEY `idx_subscribe` (`subscribe`) USING BTREE,
  KEY `idx_subscribe_time` (`subscribe_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信公众号关注用户表';
