<?php

namespace app\shopapi\controller;

use app\common\basics\Controller;
use app\shopapi\logic\PasswordResetLogic;
use app\shopapi\validate\PasswordResetValidate;
use app\common\logic\SmsLogic;
use app\api\validate\SmsSend;
use app\common\server\JsonServer;

/**
 * 商家API密码重置控制器
 * Class PasswordReset
 * @package app\shopapi\controller
 */
class PasswordReset extends Controller
{
    /**
     * 发送重置密码验证码
     * @return \think\response\Json
     */
    public function sendCode()
    {
        $client = $this->client;
        $mobile = $this->request->post('mobile');
        $key = $this->request->post('key', 'SJZHMM'); // 使用商家找回密码场景

        // 验证参数
        $validate = new PasswordResetValidate();
        if (!$validate->scene('sendCode')->check(['mobile' => $mobile])) {
            return $this->fail($validate->getError());
        }

        // 验证短信发送权限
        (new SmsSend())->goCheck('', ['mobile' => $mobile, 'key' => $key, 'client' => $client]);

        // 发送验证码
        $result = PasswordResetLogic::sendCode($mobile, $key);
        if (is_string($result)) {
            return $this->fail($result);
        }

        return $this->success('验证码发送成功', $result);
    }

    /**
     * 验证重置码
     * @return \think\response\Json
     */
    public function verifyCode()
    {
        $params = $this->request->post();

        $validate = new PasswordResetValidate();
        if (!$validate->scene('verifyCode')->check($params)) {
            return $this->fail($validate->getError());
        }

        $result = PasswordResetLogic::verifyResetCode($params);
        if (is_array($result)) {
            return $this->success('验证成功', $result);
        }

        return $this->fail($result);
    }

    /**
     * 重置密码
     * @return \think\response\Json
     */
    public function resetPassword()
    {
        $params = $this->request->post();

        $validate = new PasswordResetValidate();
        if (!$validate->scene('resetPassword')->check($params)) {
            return $this->fail($validate->getError());
        }

        $result = PasswordResetLogic::resetPassword($params);
        if ($result === true) {
            return $this->success('密码重置成功');
        }

        return $this->fail($result);
    }
}
