/**
 * 图片预览放大组件
 * 为页面中的图片添加点击放大功能
 */

(function($) {
    'use strict';

    // 图片预览功能
    var ImagePreview = {
        // 初始化
        init: function() {
            this.bindEvents();
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;
            
            // 为所有上传的图片添加点击放大功能
            $(document).on('click', '.uploaded-image, .upload-image-div img, .media-content img', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                var imgSrc = $(this).attr('src');
                if (imgSrc) {
                    self.showPreview(imgSrc, $(this));
                }
            });
            
            // 为商品列表中的图片添加放大功能
            $(document).on('click', '.image-show', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                var imgSrc = $(this).attr('src');
                if (imgSrc) {
                    self.showPreview(imgSrc, $(this));
                }
            });
            
            // 为表格中的图片添加放大功能
            $(document).on('click', 'table img, .layui-table img', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                var imgSrc = $(this).attr('src');
                if (imgSrc && imgSrc.indexOf('data:image') !== 0) { // 排除base64图片
                    self.showPreview(imgSrc, $(this));
                }
            });
        },

        // 显示预览
        showPreview: function(imgSrc, $element) {
            // 检查是否有layui的layer组件
            if (typeof layer !== 'undefined') {
                this.showLayerPreview(imgSrc, $element);
            } else {
                this.showSimplePreview(imgSrc, $element);
            }
        },

        // 使用layer组件显示预览
        showLayerPreview: function(imgSrc, $element) {
            // 获取图片标题
            var title = $element.attr('alt') || $element.attr('title') || '图片预览';
            
            // 使用layer的photos组件
            layer.photos({
                photos: {
                    "title": title,
                    "data": [{
                        "src": imgSrc,
                        "alt": title
                    }]
                },
                anim: 5,
                shade: 0.8,
                closeBtn: 1,
                area: ['auto', 'auto'],
                success: function(layero, index) {
                    // 添加自定义样式
                    $(layero).find('.layui-layer-phimg').css({
                        'max-width': '90vw',
                        'max-height': '90vh',
                        'object-fit': 'contain'
                    });
                }
            });
        },

        // 简单的预览实现（不依赖layer）
        showSimplePreview: function(imgSrc, $element) {
            var overlay = $('<div class="image-preview-overlay"></div>');
            var container = $('<div class="image-preview-container"></div>');
            var img = $('<img class="image-preview-img" src="' + imgSrc + '" alt="预览图片">');
            var closeBtn = $('<div class="image-preview-close">×</div>');
            
            container.append(img).append(closeBtn);
            overlay.append(container);
            
            // 添加样式
            overlay.css({
                'position': 'fixed',
                'top': 0,
                'left': 0,
                'width': '100%',
                'height': '100%',
                'background': 'rgba(0, 0, 0, 0.8)',
                'z-index': 99999,
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center',
                'cursor': 'pointer'
            });
            
            container.css({
                'position': 'relative',
                'max-width': '90%',
                'max-height': '90%'
            });
            
            img.css({
                'max-width': '100%',
                'max-height': '100%',
                'object-fit': 'contain',
                'display': 'block'
            });
            
            closeBtn.css({
                'position': 'absolute',
                'top': '-40px',
                'right': '0',
                'color': 'white',
                'font-size': '30px',
                'cursor': 'pointer',
                'width': '40px',
                'height': '40px',
                'text-align': 'center',
                'line-height': '40px',
                'background': 'rgba(0, 0, 0, 0.5)',
                'border-radius': '50%'
            });
            
            // 添加到页面
            $('body').append(overlay);
            
            // 绑定关闭事件
            overlay.on('click', function(e) {
                if (e.target === this || $(e.target).hasClass('image-preview-close')) {
                    overlay.remove();
                }
            });
            
            // ESC键关闭
            $(document).on('keyup.imagePreview', function(e) {
                if (e.keyCode === 27) {
                    overlay.remove();
                    $(document).off('keyup.imagePreview');
                }
            });
        },

        // 批量预览（多图片）
        showBatchPreview: function(images, currentIndex) {
            if (typeof layer === 'undefined') {
                console.warn('批量预览需要layui的layer组件');
                return;
            }
            
            var photosData = images.map(function(img, index) {
                return {
                    "src": typeof img === 'string' ? img : img.src,
                    "alt": typeof img === 'string' ? '图片' + (index + 1) : (img.alt || '图片' + (index + 1))
                };
            });
            
            layer.photos({
                photos: {
                    "title": "图片预览",
                    "data": photosData,
                    "start": currentIndex || 0
                },
                anim: 5,
                shade: 0.8,
                closeBtn: 1
            });
        }
    };

    // 自动初始化
    $(document).ready(function() {
        ImagePreview.init();
    });

    // 暴露到全局
    window.ImagePreview = ImagePreview;

    // 扩展到like对象
    if (typeof like !== 'undefined') {
        like.imagePreview = ImagePreview;
    }

})(jQuery);

// CSS样式（如果没有引入外部CSS文件）
if (!document.getElementById('image-preview-styles')) {
    var style = document.createElement('style');
    style.id = 'image-preview-styles';
    style.textContent = `
        .image-preview-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: rgba(0, 0, 0, 0.8) !important;
            z-index: 99999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
        }
        
        .image-preview-container {
            position: relative !important;
            max-width: 90% !important;
            max-height: 90% !important;
        }
        
        .image-preview-img {
            max-width: 100% !important;
            max-height: 100% !important;
            object-fit: contain !important;
            display: block !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
        }
        
        .image-preview-close {
            position: absolute !important;
            top: -40px !important;
            right: 0 !important;
            color: white !important;
            font-size: 30px !important;
            cursor: pointer !important;
            width: 40px !important;
            height: 40px !important;
            text-align: center !important;
            line-height: 40px !important;
            background: rgba(0, 0, 0, 0.5) !important;
            border-radius: 50% !important;
            transition: background 0.3s ease !important;
        }
        
        .image-preview-close:hover {
            background: rgba(0, 0, 0, 0.8) !important;
        }
        
        /* 为图片添加放大提示 */
        .uploaded-image, .upload-image-div img, .media-content img, .image-show {
            cursor: zoom-in !important;
            transition: transform 0.2s ease !important;
        }
        
        .uploaded-image:hover, .upload-image-div img:hover, .media-content img:hover, .image-show:hover {
            transform: scale(1.02) !important;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .image-preview-container {
                max-width: 95% !important;
                max-height: 95% !important;
            }
            
            .image-preview-close {
                top: -35px !important;
                font-size: 25px !important;
                width: 35px !important;
                height: 35px !important;
                line-height: 35px !important;
            }
        }
    `;
    document.head.appendChild(style);
}
