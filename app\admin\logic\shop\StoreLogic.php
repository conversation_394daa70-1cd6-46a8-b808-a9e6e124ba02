<?php


namespace app\admin\logic\shop;
use app\common\basics\Logic;
use app\common\enum\ShopEnum;
use app\common\model\goods\Goods;
use app\common\model\shop\Shop;
use app\common\model\shop\ShopAdmin;
use app\common\server\UrlServer;
use Exception;
use think\facade\Db;
use app\common\library\MeiliSearch;
use Requests;

class StoreLogic extends Logic
{


    // 迁移商品数据的方法
    public static function migrateGoods()
    {
        $goods=Db::name('goods')->where('id','>',46)->select()->toArray();
        foreach($goods as $key => $value) {
            //third_cate_id 平台商品一级分类id
            //second_cate_id 平台商品二级分类id
            //first_cate_id 平台商品三级分类id
            //goods_category分类表
            //根据二级分类获取一级分类\
            $cate_1id=0;
            $cate_2id=0;
            $cate_3id=Db::name('goods_category')->where('id', $value['third_cate_id'])->value('pid');
            if($cate_3id){
                $cate_2id=Db::name('goods_category')->where('id', $cate_3id)->value('pid');
                if($cate_2id){
                    $cate_1id=Db::name('goods_category')->where('id', $cate_2id)->value('pid');
                }
            }


            $data['first_cate_id']=$cate_1id?:$cate_2id;
            $data['second_cate_id']=$cate_3id?:$cate_2id;
            $cate3=Db::name('goods_category')->where('pid', $value['second_cate_id'])->column('id');    //随机取cate3的值
            if($cate3){
                $data['third_cate_id']=$cate3[array_rand($cate3)];
            }
            Db::name('goods')->where('id', $value['id'])->update($data);

        }
//        return '商品数据迁移成功';die;


        echo '迁移完成';die;
//

        try {
            // 读取系统A的商品数据


            $db = Db::connect('mysql2');
            $where[] = ['id', '>', 74];
            $goodsData = $db->table('fa_wanlshop_goods')->where($where)->select()->toArray();

            // 遍历商品数据并插入到系统B
            foreach ($goodsData as $good) {
                $sku_data=$db->table('fa_wanlshop_goods_spu')->where(['goods_id'=>['=',$good['id']]])->select()->toArray();

                //只获取多规格商品
                $sku_nums=count($sku_data);
                if($sku_nums>1){
                    // 字段映射和数据转换
                    $cate_name=$db->table('fa_wanlshop_category')->where('id',$good['category_id'])->value('name');
                    $cate_id= Db::name('goods_category')->where('name', $cate_name)->value('id');
                    $top="https://jcstatics.jiaqingfu.com.cn/";
                    //判断图片是否存在,不存在则跳过
                    $imageUrl = $top . $good['image'];
                    $insertData = [
                        'type' => 0, // 假设所有商品都是实物商品
                        'name' => $good['title'],
                        'code' => '', // 可能需要生成或映射商品编码
                        'shop_id' =>rand(48,49),
                        'shop_cate_id' =>$cate_id?:1, // 可能需要映射到系统B的分类ID
                        'first_cate_id' => 0, // 可能需要映射到系统B的一级分类ID
                        'second_cate_id' => 0, // 可能需要映射到系统B的二级分类ID
                        'third_cate_id' => 0, // 可能需要映射到系统B的三级分类ID
                        'brand_id' => $good['brand_id'], // 可能需要映射到系统B的品牌ID
                        'unit_id' => 0, // 可能需要设置商品单位ID
                        'supplier_id' => 0, // 可能需要设置供应商ID
                        'status' => 1, // 假设所有商品都上架
                        'image' => $top.$good['image'],
                        'video' => $top.$good['videos'],
                        'remark' => $good['description'],
                        'content' => $good['content'],
                        'sort' => 255, // 默认排序
                        'sales_actual' => $good['sales'],
                        'clicks' => $good['views'],
                        'clicks_virtual' => 0,
                        'spec_type' =>2, // 单规格或多规格
                        'max_price' => $good['price'], // 假设所有SKU价格相同
                        'min_price' => $good['price'],
                        'market_price' => $good['price'], // 假设市场价等于商品价格
                        'stock' => '9999', // 假设有库存数量字段
                        'express_type' => 1, // 默认包邮
                        'express_money' => 0.00,
                        'express_template_id' => $good['freight_id'], // 可能需要映射到系统B的运费模板ID
                        'is_recommend' => 0, // 默认不推荐
                        'audit_status' => 1, // 默认审核通过
                        'audit_remark' => '',
                        'create_time' => strtotime($good['createtime']),
                        'update_time' => strtotime($good['updatetime']),
                        'del' => 0,
                        'stock_warn' => 0,
                        'column_ids' => '',
                        'sales_virtual' => 0,
                        'sort_weight' => 255,
                        'poster' => '',
                        'is_show_stock' => 0,
                        'is_member' => 0,
                        'is_distribution' => $good['distribution'] === 'true' ? 1 : 0,
                        'first_ratio' => $good['shequ_bili'],
                        'second_ratio' => $good['quyu_bili'],
                        'third_ratio' => $good['service_bili'],
                        'delivery_type' => '1', // 默认快递发货
                        'after_pay' => 0,
                        'after_delivery' => 0,
                        'is_list' => 0,
                        'join_jc' => 0,
                        'hgou_lv' => 0.0,
                        'year_jc_sales' => 0,
                        'year_sales' => $good['sales'],
                        'collect_nums' => $good['like'],
                        'reshare_nums' => 0,
                        'share_nums' => 0,
                        'goods_label_top' => '',
                        'goods_label' => '',
                        'is_hot' => strpos($good['flag'], 'hot') !== false ? 1 : 0, // 判断是否为热门商品
                        'delivery_content' => '',
                    ];

                    // 插入到系统B的商品表
                    $goodsId=Db::name('goods')->insertGetId($insertData);
                    //插入规格表ls_goods_spec

                    foreach($sku_data as $sku_v=>$sku_k) {
                        $sku_spec = [
                            'name' => $sku_k['name'],
                            'goods_id' =>$goodsId
                        ];
                        $spec_ids=Db::name('goods_spec')->insertGetId($sku_spec);

                        //截取spu
                        $spu_datas = explode(',', $sku_k['item']);
                        $str_array=[];
                        $specs=[];
                        foreach ($spu_datas as $a => $b) {
                            if(!in_array($b,$str_array)){
                                $specs[] = [
                                    'goods_id' => $goodsId,
                                    'spec_id' => $spec_ids,
                                    'value' => $b
                                ];
                                $str_array[]=$b;
                            }

                        }
                        Db::name('goods_spec_value')->insertAll($specs);


                    }

                    // 处理SKU数据（如果有）
                    $skuData = $db->table('fa_wanlshop_goods_sku')->where('goods_id', $good['id'])->select();
                    foreach ($skuData as $sku) {
                        $spec_id=Db::name('goods_spec_value')->where('goods_id',$goodsId)->whereRaw("FIND_IN_SET(value,?)", [$sku['difference']])->column('id');

                        $spec_value_ids= implode(',',$spec_id);
                        $skuInsertData = [
                            'image' => $top . $sku['thumbnail'],
                            'goods_id' => $goodsId,
                            'spec_value_ids' =>$spec_value_ids, // 可能需要映射规格值ID
                            'spec_value_str' => $sku['difference'], // 可能需要映射规格值名称
                            'market_price' => $sku['market_price'],
                            'price' => $sku['price'],
                            'stock' => $sku['stock'],
                            'volume' => 0.000, // 假设没有体积数据
                            'weight' => 0.000, // 假设没有重量数据
                            'bar_code' => $sku['sn'],
                            'chengben_price' => $sku['cost_price'],
                            'pdjc_price' => 0.00, // 假设没有拼单集采价
                        ];
                        // 插入到系统B的SKU表
                        Db::name('goods_item')->insert($skuInsertData);

                    }
                }

            }

            return '商品数据迁移成功';

        } catch (\Exception $e) {

            return  $e->getMessage();
        }
    }

    /**
     * NOTE: 商家列表
     * @author: 张无忌
     * @param $get
     * @return array|bool
     */
    public static function lists($get)
    {
        try {
            $where = [
                ['del', '=', 0]
            ];
            //yan_time 检验到期时间0未缴费 为时间戳
            //待验厂
            if (isset($get['is_yan']) && $get['is_yan'] != '') {
                if($get['is_yan']==1){
                    $where[] = ['yan_time', '>', 0];
                    $where[] = ['yan_level', '>', 0];
                }else if($get['is_yan']==2){
                    $where[] = ['yan_time', '>', 0];
                    $where[] = ['yan_level', '=', 0];
                }else{
                    $where[] = ['yan_time', '=', 0];
                }

            }
            if (!empty($get['name']) and $get['name'])
                $where[] = ['name', 'like', '%'.$get['name'].'%'];

            if (!empty($get['type']) and is_numeric($get['type']))
                $where[] = ['yan_level', '=', $get['type']];

            // 添加商家等级筛选
            if (isset($get['tier_level']) && $get['tier_level'] !== '')
                $where[] = ['tier_level', '=', $get['tier_level']];

            if (!empty($get['cid']) and is_numeric($get['cid']))
                $where[] = ['cid', '=', $get['cid']];

            if (isset($get['is_recommend']) && $get['is_recommend'] != '')
                $where[] = ['is_recommend', '=', $get['is_recommend']];

            if (isset($get['is_run']) && $get['is_run'] != '')
                $where[] = ['is_run', '=', $get['is_run']];

            if (isset($get['is_freeze']) and $get['is_freeze'] != '')
                $where[] = ['is_freeze', '=', $get['is_freeze']];

            if (!empty($get['expire_start_time']) and $get['expire_start_time'])
                $where[] = ['expire_time', '>=', strtotime($get['expire_start_time'])];

            if (!empty($get['expire_end_time']) and $get['expire_end_time'])
                $where[] = ['expire_time', '<=', strtotime($get['expire_end_time'])];

            $condition = 'del=0';
            // 到期状态
            if (isset($get['expire_status']) and $get['expire_status'] != '') {
                if ($get['expire_status']) {
                    // 已到期
                    $where[] = ['expire_time', '<', time()];
                    $where[] = ['expire_time', '>', 0];
                } else {
                    // 未到期
                    $condition = "expire_time=0 OR expire_time >". time();
                }
            }

            $model = new Shop();
            $lists = $model->field(true)
                ->where($where)
                ->whereRaw($condition)
                ->order('id', 'desc')
                ->order('weight', 'asc')
                ->with(['category', 'admin'])
                ->append(['expire_desc'])
                ->paginate([
                    'page'      => $get['page'],
                    'list_rows' => $get['limit'],
                    'var_page' => 'page'
                ])
                ->toArray();

            // 调试输出，查看返回的数据结构
            // var_dump($lists['data'][0]);die;

            foreach ($lists['data'] as &$item) {
                $item['category']  = $item['category']['name'] ?? '未知';
                $item['type']      = ShopEnum::getShopTypeDesc($item['type']);
                $item['is_run']    = ShopEnum::getShopIsRunDesc($item['is_run']);
                $item['is_freeze'] = ShopEnum::getShopFreezeDesc($item['is_freeze']);
                $item['is_recommend'] = ShopEnum::getShopIsRecommendDesc($item['is_recommend']);
                $item['account'] = $item['admin']['account'] ?? '';
                $item['level_info_image']='';
                $item['level_info_name']='';
                $item['yan_time']=$item['yan_time']?date('Y-m-d',$item['yan_time']):0;

                // 添加商家等级信息
                $tierNames = [
                    0 => '0元入驻',
                    1 => '商家会员',
                    2 => '实力厂商'
                ];
                $item['tier_level_name'] = $tierNames[$item['tier_level']] ?? '未知等级';
                $item['tier_expire_time_text'] = $item['tier_expire_time'] > 0 ? date('Y-m-d', $item['tier_expire_time']) : '永久';

                if($item['yan_level']){
                    $level_info=Db::name('shop_level')->where('id',$item['yan_level'])->find();
                    $item['level_info_name']=$level_info['name'];
                    $item['level_info_image']=UrlServer::getFileUrl($level_info['image']);
                }

                // 添加检验人员昵称
                if ($item['f_user'] > 0) {
                    $staff = \app\common\model\user\User::where('id', $item['f_user'])->value('nickname');
                    $item['staff_nickname'] = $staff ?: '未知';
                } else {
                    $item['staff_nickname'] = '';
                }
                if(!$item['logo']){
                    $item['logo']='https://jcstatics.jiaqingfu.com.cn/applet/static/image.png';
                }
                // 获取检验记录状态
                $inspection = \app\common\model\shop\ShopInspection::where('shop_id', $item['id'])
                    ->where('is_deleted', 0)
                    ->order('id', 'desc')
                    ->find();

                $item['inspection_status'] = null;
                $item['has_inspection_record'] = false;
                if ($inspection) {
                    $item['inspection_status'] = $inspection['status']; // 0-待审核，1-已通过，2-未通过
                    $item['has_inspection_record'] = true;
                }
                 $item['goods_count'] = \app\common\model\goods\Goods::where('shop_id', $item['id'])->count();
            }

            return ['count'=>$lists['total'], 'lists'=>$lists['data']];
        } catch (Exception $e) {
            return ['error'=>$e->getMessage()];
        }
    }

    /**
     * NOTE: 商家详细
     * @author: 张无忌
     * @param $id
     * @return array
     */
    public static function detail($id)
    {
        $model = new Shop();
        $detail = $model->json(['other_qualifications'],true)->findOrEmpty($id)->toArray();
        $detail['expire_time'] = $detail['expire_time'] == '无期限' ? 0 : $detail['expire_time'];
        $detail['category'] = $detail['category'] ?? '未知';
        $detail['business_license'] = $detail['business_license'] ? UrlServer::getFileUrl($detail['business_license']) : '';
        if (!empty($detail['other_qualifications'])) {
            foreach ($detail['other_qualifications'] as &$val) {
                $val = UrlServer::getFileUrl($val);
            }
        }

        return $detail;
    }




    public static function getAccountInfo($id)
    {
        $detail = ShopAdmin::field('id,account')->where(['shop_id' => $id, 'root' => 1])->findOrEmpty()->toArray();
        return $detail;
    }

    /**
     * NOTE: 新增商家
     * @author: 张无忌
     * @param $post
     * @return bool
     */
    public static function add($post)
    {
        Db::startTrans();
        try {
            // 设置默认值
            if (empty($post['delivery_type'])) {
                $post['delivery_type'] = [1]; // 默认快递发货
            }
            if (empty($post['type'])) {
                $post['type'] = 2; // 默认入驻商家
            }

            // 校验配送方式
            self::checkDeliveryType($post);

            // 创建商家
            $shop = Shop::create([
                'cid'               => $post['cid'],
                'type'              => $post['type'],
                'name'              => $post['name'],
                'nickname'          => $post['nickname'],
                'mobile'            => $post['mobile'],
                'logo'              => $post['logo'] ?? '',
                'background'        => $post['background'] ?? '',
                'license'           => $post['license'] ?? '',
                'keywords'          => $post['keywords'] ?? '',
                'intro'             => $post['intro'] ?? '',
                'weight'            => $post['weight'] ?? 0,
                'trade_service_fee' => $post['trade_service_fee'],
                'is_run'            => $post['is_run'],
                'is_freeze'         => $post['is_freeze'],
                'is_product_audit'  => $post['is_product_audit'],
                'is_recommend'      => $post['is_recommend'] ?? 0,
                'expire_time'       => !empty($post['expire_time']) ? strtotime($post['expire_time']) : 0,
                'province_id'    => $post['province_id'] ?? 0,
                'city_id'        => $post['city_id'] ?? 0,
                'district_id'    => $post['district_id'] ?? 0,
                'address'        => $post['address'] ?? '',
                'longitude'      => $post['longitude'] ?? '',
                'latitude'       => $post['latitude'] ?? '',
                'delivery_type'  => $post['delivery_type'] ?? [1]
            ]);
            // 创建账号
            // 新增商家登录账号
            $time = time();
            $salt = substr(md5($time . $post['name']), 0, 4);//随机4位密码盐
            ShopAdmin::create([
                'root' => 1,
                'shop_id' => $shop->id,
                'name' => '超级管理员',
                'account' => $post['account'],
                'password' => generatePassword($post['password'], $salt),
                'salt' => $salt,
                'role_id' => 0,
                'create_time' => $time,
                'update_time' => $time,
                'disable' => 0,
                'del' => 0
            ]);

            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * NOTE: 编辑商家
     * @author: 张无忌
     * @param $post
     * @return bool
     */
    public static function edit($post)
    {
        try {
            // 设置默认值
            if (empty($post['delivery_type'])) {
                $post['delivery_type'] = [1]; // 默认快递发货
            }
            if (empty($post['type'])) {
                $post['type'] = 2; // 默认入驻商家
            }

            // 校验配送方式
            self::checkDeliveryType($post);

            Shop::update([
                'cid'               => $post['cid'],
                'type'              => $post['type'],
                'name'              => $post['name'],
                'nickname'          => $post['nickname'],
                'mobile'            => $post['mobile'],
                'logo'              => $post['logo'] ?? '',
                'keywords'          => $post['keywords'] ?? '',
                'intro'             => $post['intro'] ?? '',
                'trade_service_fee' => $post['trade_service_fee'],
                'is_run'            => $post['is_run'],
                'is_freeze'         => $post['is_freeze'],
                'is_product_audit'  => $post['is_product_audit'],
                'expire_time'       => !empty($post['expire_time']) ? strtotime($post['expire_time']) : 0,
                'province_id'    => $post['province_id'] ?? 0,
                'city_id'        => $post['city_id'] ?? 0,
                'district_id'    => $post['district_id'] ?? 0,
                'address'        => $post['address'] ?? '',
                'longitude'      => $post['longitude'] ?? '',
                'latitude'       => $post['latitude'] ?? '',
                'delivery_type'  => $post['delivery_type'] ?? [1],
                'business_license'   => empty($post['business_license']) ? '' : UrlServer::setFileUrl($post['business_license']),
                'other_qualifications' => isset($post['other_qualifications']) ? json_encode($post['other_qualifications'], JSON_UNESCAPED_UNICODE) : '',
            ], ['id'=>$post['id']]);

            return true;
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * NOTE: 设置商家
     * @author: 张无忌
     * @param $post
     * @return bool
     */
    public static function set($post)
    {
        try {
            Shop::update([
                'is_distribution' => $post['is_distribution'] ?? 0,
                'is_recommend' => $post['is_recommend'] ?? 0,
                'yan_level' => $post['yan_level'] ?? 0,
                'is_pay' => $post['is_pay'] ?? 1, //是否开启支付功能,默认开启
                'weight'       => $post['weight']
            ], ['id'=>$post['id']]);

            return true;
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * NOTE: 更新账号密码
     * @author: 张无忌
     * @param $post
     * @return bool
     */
    public static function account($post)
    {
        Db::startTrans();
        try {
            if(!isset($post['account']) || empty($post['account'])) {
                throw new \think\Exception('账户不能为空');
            }
            $shopAdmin = ShopAdmin::where([
                ['account', '=', trim($post['account'])],
                ['shop_id', '<>', $post['id']]
            ])->findOrEmpty();
            if(!$shopAdmin->isEmpty()) {
                throw new \think\Exception('账户已存在，请更换其他名称重试');
            }

            $shopAdmin = ShopAdmin::where(['shop_id' => $post['id'], 'root' => 1])->findOrEmpty();

            $shopAdminUpdateData = [
                'account'     => $post['account'],
                'update_time' => time()
            ];
            if (!empty($post['password'])) {
                $shopAdminUpdateData['password'] = generatePassword($post['password'], $shopAdmin->salt);
            }
            ShopAdmin::where(['shop_id' => $post['id'], 'root' => 1])->update($shopAdminUpdateData);

            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            static::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 批量更新商家营业状态或冻结状态
     * @param $ids
     * @param $field
     * @param $value
     * @return Shop|false
     * <AUTHOR>
     * @date 2022/3/17 10:42
     */
    public static function batchOperation($ids, $field, $value)
    {
        try {
            $result = Shop::whereIn('id', $ids)->update([
                $field  => $value,
                'update_time' => time()
            ]);
            return $result;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 搜索检验人员
     * @param $get
     * @return array
     * <AUTHOR> @date
     */
    public static function searchStaff($get)
    {
        try {
            $where = [
                ['del', '=', 0],
                ['disable', '=', 0]
            ];

            // 根据搜索条件查询
            if (!empty($get['search_type']) && !empty($get['search_value'])) {
                if ($get['search_type'] == 'mobile') {
                    $where[] = ['mobile', 'like', '%' . $get['search_value'] . '%'];
                } elseif ($get['search_type'] == 'nickname') {
                    $where[] = ['nickname', 'like', '%' . $get['search_value'] . '%'];
                }
            }

            // 查询用户
            $users = \app\common\model\user\User::field('id, nickname, mobile')
                ->where($where)
                ->limit(10)
                ->select()
                ->toArray();

            return $users;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return [];
        }
    }

    /**
     * @notes 分配检验人员
     * @param $post
     * @return bool
     * <AUTHOR> @date
     */
    public static function assignStaff($post)
    {
        try {
            if (empty($post['shop_id']) || empty($post['staff_id'])) {
                self::$error = '参数错误';
                return false;
            }

            // 检查商家是否存在
            $shop = Shop::where('id', $post['shop_id'])->find();
            if (!$shop) {
                self::$error = '商家不存在';
                return false;
            }

            // 检查商家是否符合分配条件
            if ($shop['yan_fee'] != 1 || $shop['yan_time'] > 0) {
                self::$error = '该商家不符合分配检验人员的条件';
                return false;
            }

            // 检查检验人员是否存在
            $staff = \app\common\model\user\User::where('id', $post['staff_id'])->find();
            if (!$staff) {
                self::$error = '检验人员不存在';
                return false;
            }

            // 更新商家的检验人员
            $result = Shop::update([
                'f_user' => $post['staff_id'],
                'update_time' => time()
            ], ['id' => $post['shop_id']]);

            // 记录日志，方便调试
            \think\facade\Log::info('分配检验人员：' . json_encode([
                'shop_id' => $post['shop_id'],
                'staff_id' => $post['staff_id'],
                'result' => $result
            ]));

            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 校验配送方式
     * @param $post
     * @return bool
     * @throws \Exception
     * <AUTHOR>
     * @date 2022/11/1 11:30
     */
    public static function checkDeliveryType($post)
    {
        // 校验配送方式
        if (empty($post['delivery_type'])) {
            throw new \Exception('至少选择一种配送方式');
        }

        // 线下自提时，商家地址必填
        if (in_array(ShopEnum::DELIVERY_SELF, $post['delivery_type'])) {
            if (empty($post['province_id']) || empty($post['city_id']) || empty($post['district_id']) || empty($post['address'])) {
                throw new \Exception('线下自提需完善商家地址');
            }
        }
        return true;
    }

    /**
     * @notes 获取商家检验信息
     * @param $shop_id
     * @return array
     * <AUTHOR> @date
     */
    public static function getInspection($shop_id)
    {
        try {
            // 添加调试日志
            \think\facade\Log::info('开始查询检验信息 - shop_id: ' . $shop_id);

            $inspection = \app\common\model\shop\ShopInspection::where('shop_id', $shop_id)
                ->where('is_deleted', 0)
                ->order('id', 'desc')
                ->findOrEmpty()
                ->toArray();
       
            \think\facade\Log::info('原始检验数据: ' . json_encode($inspection, JSON_UNESCAPED_UNICODE));

            if (!empty($inspection)) {
                // 处理图片数据
                if (!empty($inspection['images'])) {
                    $images = json_decode($inspection['images'], true);
                    \think\facade\Log::info('解析图片数据: ' . json_encode($images, JSON_UNESCAPED_UNICODE));

                    if (is_array($images)) {
                        foreach ($images as &$image) {
                            $image = \app\common\server\UrlServer::getFileUrl($image);
                        }
                        $inspection['images'] = $images;
                    } else if (is_string($images) && !empty($images)) {
                        // 如果解析后是字符串，转换为数组
                        $inspection['images'] = [\app\common\server\UrlServer::getFileUrl($images)];
                    } else {
                        $inspection['images'] = [];
                    }
                } else {
                    $inspection['images'] = [];
                }

                // 处理视频数据
                if (!empty($inspection['videos'])) {
                    $videos = json_decode($inspection['videos'], true);
                    \think\facade\Log::info('解析视频数据: ' . json_encode($videos, JSON_UNESCAPED_UNICODE));
                    \think\facade\Log::info('视频数据类型: ' . gettype($videos));

                    if (is_array($videos)) {
                        foreach ($videos as &$video) {
                            $video = \app\common\server\UrlServer::getFileUrl($video);
                        }
                        $inspection['videos'] = $videos;
                    } else if (is_string($videos) && !empty($videos)) {
                        // 如果解析后是字符串，转换为数组
                        $inspection['videos'] = [\app\common\server\UrlServer::getFileUrl($videos)];
                    } else if (json_decode($inspection['videos'], true) === null && !empty($inspection['videos'])) {
                        // 如果JSON解析失败，可能是直接存储的字符串
                        \think\facade\Log::info('视频数据不是JSON格式，直接作为字符串处理: ' . $inspection['videos']);
                        $inspection['videos'] = [\app\common\server\UrlServer::getFileUrl($inspection['videos'])];
                    } else {
                        $inspection['videos'] = [];
                    }
                } else {
                    $inspection['videos'] = [];
                }
   
                // 获取检验人员信息
                if ($inspection['staff_id'] > 0) {
                    $staff = \app\common\model\user\User::where('id', $inspection['staff_id'])->value('nickname');
                    $inspection['staff_name'] = $staff ?: '未知';
                } else {
                    $inspection['staff_name'] = '';
                }

                // 获取审核人员信息
                if (!empty($inspection['audit_user_id']) && $inspection['audit_user_id'] > 0) {
                    $auditUser = \app\common\model\admin\Admin::where('id', $inspection['audit_user_id'])->value('username');
                    $inspection['audit_user'] = $auditUser ?: '未知';
                } else {
                    $inspection['audit_user'] = '';
                }

                \think\facade\Log::info('处理后的检验数据: ' . json_encode($inspection, JSON_UNESCAPED_UNICODE));
            } else {
                \think\facade\Log::info('没有找到检验数据');
            }

            return $inspection;
        } catch (\Exception $e) {
            \think\facade\Log::error('获取检验信息失败: ' . $e->getMessage());
            self::$error = $e->getMessage();
            return [];
        }
    }

    /**
     * @notes 获取商家名称
     * @param $shop_id
     * @return string
     * <AUTHOR> @date
     */
    public static function getShopName($shop_id)
    {
        try {
            return \app\common\model\shop\Shop::where('id', $shop_id)->value('name') ?: '未知商家';
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return '未知商家';
        }
    }

    /**
     * @notes 保存商家检验信息
     * @param $post
     * @return bool
     * <AUTHOR> @date
     */
    public static function saveInspection($post)
    {
        \think\facade\Db::startTrans();
        try {
            if (empty($post['shop_id'])) {
                throw new \Exception('商家ID不能为空');
            }

            $shop = \app\common\model\shop\Shop::where('id', $post['shop_id'])->find();
            if (!$shop) {
                throw new \Exception('商家不存在');
            }

            $inspection = \app\common\model\shop\ShopInspection::where('shop_id', $post['shop_id'])
                ->where('is_deleted', 0)
                ->order('id', 'desc')
                ->find();

            $time = time();
            // 处理图片数据
            $images = [];
            if (!empty($post['images'])) {
                if (is_array($post['images'])) {
                    $images = $post['images'];
                } else {
                    $images = [$post['images']];
                }
            }

            // 处理视频数据
            $videos = [];
            if (!empty($post['videos'])) {
                if (is_array($post['videos'])) {
                    $videos = $post['videos'];
                } else {
                    // 如果是单个视频字符串，转换为数组
                    $videos = [$post['videos']];
                }
            }

            \think\facade\Log::info('保存检验信息 - 处理后的图片数据: ' . json_encode($images, JSON_UNESCAPED_UNICODE));
            \think\facade\Log::info('保存检验信息 - 处理后的视频数据: ' . json_encode($videos, JSON_UNESCAPED_UNICODE));

            $data = [
                'shop_id' => $post['shop_id'],
                'staff_id' => $shop['f_user'] ?? 0,
                'images' => json_encode($images, JSON_UNESCAPED_UNICODE),
                'videos' => json_encode($videos, JSON_UNESCAPED_UNICODE),
                'description' => $post['description'] ?? '',
                'status' => 0, // 提交后设为待审核状态
                'reason' => '',
                'update_time' => $time
            ];

            if (!$inspection) {
                $data['create_time'] = $time;
                \app\common\model\shop\ShopInspection::create($data);
            } else {
                \app\common\model\shop\ShopInspection::update($data, ['id' => $inspection['id']]);
            }

            // 不再自动更新yan_time字段，由审核通过时更新

            \think\facade\Db::commit();
            return true;
        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 审核商家检验信息
     * @param array $data
     * @return array
     */
    public static function auditInspection($data)
    {
        try {
            $inspection_id = $data['inspection_id'];
            $status = $data['status']; // 1-通过，2-未通过
            $reason = $data['reason'] ?? '';
            
            if (!$inspection_id) {
                return ['code' => false, 'msg' => '检验记录不存在'];
            }
            
            $update_data = [
                'status' => $status,
                'audit_time' => time(),
                'audit_user_id' => session('admin.id'),
                'update_time' => time()
            ];
            
            if ($status == 2 && $reason) {
                $update_data['reason'] = $reason;
            }
            
            $result = \app\common\model\shop\ShopInspection::where('id', $inspection_id)->update($update_data);

            if ($result) {
                // 如果审核通过，更新商家的yan_time字段
                if ($status == 1) {
                    $inspection = \app\common\model\shop\ShopInspection::where('id', $inspection_id)->find();
                    if ($inspection) {
                        \app\common\model\shop\Shop::update(['yan_time' => time()], ['id' => $inspection['shop_id']]);
                    }
                }

                $status_text = $status == 1 ? '通过' : '未通过';
                return ['code' => true, 'msg' => '审核' . $status_text . '成功'];
            } else {
                return ['code' => false, 'msg' => '审核失败'];
            }
        } catch (\Exception $e) {
            return ['code' => false, 'msg' => '审核失败：' . $e->getMessage()];
        }
    }
}
