<?php

namespace app\shop\validate;

use think\Validate;

/**
 * 密码重置验证器
 * Class PasswordResetValidate
 * @package app\shop\validate
 */
class PasswordResetValidate extends Validate
{
    protected $rule = [
        'mobile' => 'require|mobile',
        'code' => 'require|length:6|number',
        'token' => 'require|alphaNum|length:32',
        'password' => 'require|min:6|max:20|checkPasswordStrength',
        'confirm_password' => 'require|confirm:password'
    ];

    protected $message = [
        'mobile.require' => '手机号不能为空',
        'mobile.mobile' => '手机号格式不正确',
        'code.require' => '验证码不能为空',
        'code.length' => '验证码必须为6位数字',
        'code.number' => '验证码只能包含数字',
        'token.require' => '重置令牌不能为空',
        'token.alphaNum' => '重置令牌格式不正确',
        'token.length' => '重置令牌长度不正确',
        'password.require' => '密码不能为空',
        'password.min' => '密码长度不能少于6位',
        'password.max' => '密码长度不能超过20位',
        'password.checkPasswordStrength' => '密码强度不够，请包含字母、数字或特殊字符',
        'confirm_password.require' => '确认密码不能为空',
        'confirm_password.confirm' => '两次输入的密码不一致'
    ];

    protected $scene = [
        'sendCode' => ['mobile'],
        'verifyCode' => ['mobile', 'code'],
        'resetPassword' => ['token', 'password', 'confirm_password']
    ];

    /**
     * 检查密码强度
     * @param $value
     * @param $rule
     * @param $data
     * @return bool|string
     */
    protected function checkPasswordStrength($value, $rule, $data)
    {
        // 密码必须包含字母、数字或特殊字符中的至少两种
        $hasLetter = preg_match('/[a-zA-Z]/', $value);
        $hasNumber = preg_match('/[0-9]/', $value);
        $hasSpecial = preg_match('/[^a-zA-Z0-9]/', $value);

        $count = $hasLetter + $hasNumber + $hasSpecial;

        if ($count < 2) {
            return false;
        }

        return true;
    }
}
