<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品规格缺货显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .goods-detail {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .spec-group {
            margin-bottom: 20px;
        }
        .spec-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .spec-values {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .spec-value {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        .spec-value.available {
            border-color: #007bff;
            color: #007bff;
        }
        .spec-value.available:hover {
            background-color: #007bff;
            color: white;
        }
        .spec-value.selected {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .spec-value.out-of-stock {
            background-color: #f8f9fa;
            color: #6c757d;
            border-color: #dee2e6;
            cursor: not-allowed;
            position: relative;
        }
        .spec-value.out-of-stock::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #6c757d;
            transform: translateY(-50%);
        }
        .stock-info {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        .out-of-stock-text {
            color: #dc3545;
            font-size: 12px;
        }
        .demo-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1 class="demo-title">商品规格缺货显示优化演示</h1>
    
    <div class="goods-detail">
        <h2>测试商品</h2>
        
        <div id="spec-container">
            <!-- 规格将通过JavaScript动态生成 -->
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
            <h4>功能说明：</h4>
            <ul>
                <li>✅ 有库存的规格值：蓝色边框，可点击选择</li>
                <li>❌ 缺货的规格值：灰色显示，带删除线，不可点击</li>
                <li>📊 显示具体库存数量</li>
                <li>🚫 缺货时显示"缺货"提示</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟从API获取的商品数据
        const goodsData = {
            id: 1,
            name: '测试商品',
            goods_spec: [
                {
                    id: 1,
                    name: '颜色',
                    spec_value: [
                        { id: 1, value: '红色', stock: 10, is_stock_out: 0 },
                        { id: 2, value: '蓝色', stock: 0, is_stock_out: 1 },
                        { id: 3, value: '绿色', stock: 5, is_stock_out: 0 }
                    ]
                },
                {
                    id: 2,
                    name: '尺寸',
                    spec_value: [
                        { id: 4, value: 'S', stock: 3, is_stock_out: 0 },
                        { id: 5, value: 'M', stock: 0, is_stock_out: 1 },
                        { id: 6, value: 'L', stock: 8, is_stock_out: 0 },
                        { id: 7, value: 'XL', stock: 0, is_stock_out: 1 }
                    ]
                }
            ]
        };

        // 渲染规格选择器
        function renderSpecs() {
            const container = document.getElementById('spec-container');
            
            goodsData.goods_spec.forEach(spec => {
                const specGroup = document.createElement('div');
                specGroup.className = 'spec-group';
                
                const title = document.createElement('div');
                title.className = 'spec-title';
                title.textContent = spec.name + '：';
                specGroup.appendChild(title);
                
                const valuesContainer = document.createElement('div');
                valuesContainer.className = 'spec-values';
                
                spec.spec_value.forEach(specValue => {
                    const valueElement = document.createElement('div');
                    valueElement.className = 'spec-value';
                    valueElement.dataset.specId = spec.id;
                    valueElement.dataset.valueId = specValue.id;
                    
                    // 根据库存状态设置样式和行为
                    if (specValue.is_stock_out === 1) {
                        valueElement.classList.add('out-of-stock');
                        valueElement.innerHTML = `
                            ${specValue.value}
                            <div class="stock-info out-of-stock-text">缺货</div>
                        `;
                    } else {
                        valueElement.classList.add('available');
                        valueElement.innerHTML = `
                            ${specValue.value}
                            <div class="stock-info">库存: ${specValue.stock}</div>
                        `;
                        
                        // 添加点击事件
                        valueElement.addEventListener('click', function() {
                            // 清除同规格其他选项的选中状态
                            const sameSpecValues = valuesContainer.querySelectorAll('.spec-value.available');
                            sameSpecValues.forEach(el => el.classList.remove('selected'));
                            
                            // 设置当前选项为选中状态
                            this.classList.add('selected');
                            
                            console.log(`选择了 ${spec.name}: ${specValue.value}`);
                        });
                    }
                    
                    valuesContainer.appendChild(valueElement);
                });
                
                specGroup.appendChild(valuesContainer);
                container.appendChild(specGroup);
            });
        }

        // 页面加载完成后渲染规格
        document.addEventListener('DOMContentLoaded', renderSpecs);
    </script>
</body>
</html>
