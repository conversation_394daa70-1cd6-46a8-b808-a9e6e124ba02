<?php


namespace app\common\server;

use app\common\enum\FileEnum;
use app\common\model\File;
use app\common\server\storage\Driver as StorageDriver;
use EasyWeChat\Factory;
use think\Exception;

class FileServer
{
    /**
     * NOTE: 图片上传
     * @author: 张无忌
     * @param $cid
     * @param string $save_dir
     * @return array
     * @throws Exception
     */
    public static function image($cid, $shop_id, $user_id = 0,$save_dir='uploads/images')
    {
        try {
            // 1、获取当前存储对象
            $config = [
                'default' => ConfigServer::get('storage', 'default', 'local'),
                'engine' => ConfigServer::get('storage_engine') ?? ['local'=>[]]
            ];

            // 2、执行文件上传
            $StorageDriver = new StorageDriver($config);
            $StorageDriver->setUploadFile('file');

            $fileName = $StorageDriver->getFileName();
            $fileInfo = $StorageDriver->getFileInfo();

            // 验证是否是图片文件
            if (! check_is_image($fileInfo['realPath'] ?? '')) {
                throw new Exception('不是有效的图像文件');
            }

            // 校验上传文件后缀
            if (!in_array(strtolower($fileInfo['ext']), config('project.file_image'))) {
                throw new Exception("上传图片不允许上传". $fileInfo['ext'] . "文件");
            }

            // 上传文件
            $save_dir = $save_dir . '/' .  date('Ymd');
            if (!$StorageDriver->upload($save_dir)) {
                throw new Exception($StorageDriver->getError());
            }

            // 3、处理文件名称
            if (strlen($fileInfo['name']) > 128) {
                $file_name = substr($fileInfo['name'], 0, 123);
                $file_end = substr($fileInfo['name'], strlen($fileInfo['name'])-5, strlen($fileInfo['name']));
                $fileInfo['name'] = $file_name.$file_end;
            }

            // 4、写入数据库中
            $file = File::create([
                'cid'  => $cid,
                'type' => FileEnum::IMAGE_TYPE,
                'name' => $fileInfo['name'],
                'uri'  => $save_dir . '/' . str_replace("\\","/", $fileName),
                'shop_id' => $shop_id,
                'user_id' => $user_id,
                'create_time' => time(),
            ]);

            // 5、返回结果
            return [
                'id'   => $file['id'],
                'cid'  => $file['cid'],
                'type' => $file['type'],
                'name' => $file['name'],
                'shop_id' => $file['shop_id'],
                'uri'  => UrlServer::getFileUrl($file['uri']),
                'base_uri'  => $file['uri']
            ];

        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 视频上传
     */
    public static function video($cid, $shop_id, $user_id=0,$save_dir='uploads/video')
    {
        try {
            // 1、获取当前存储对象
            $config = [
                'default' => ConfigServer::get('storage', 'default', 'local'),
                'engine' => ConfigServer::get('storage_engine') ?? ['local'=>[]]
            ];

            // 2、执行文件上传
            $StorageDriver = new StorageDriver($config);
            $StorageDriver->setUploadFile('file');
            $fileName = $StorageDriver->getFileName();
            $fileInfo = $StorageDriver->getFileInfo();

            // 验证是否是视频
            if (! check_is_video($StorageDriver->getFileInfo()['realPath'] ?? '')) {
                throw new Exception('不是有效的视频文件');
            }

            // 校验上传文件后缀
            if (!in_array(strtolower($fileInfo['ext']), config('project.file_video'))) {
                throw new Exception("上传视频不允许上传". $fileInfo['ext'] . "文件");
            }

            // 上传文件
            $save_dir = $save_dir . '/' .  date('Ymd');
            if (!$StorageDriver->upload($save_dir)) {
                throw new Exception($StorageDriver->getError());
            }

            // 3、处理文件名称
            if (strlen($fileInfo['name']) > 128) {
                $file_name = substr($fileInfo['name'], 0, 123);
                $file_end = substr($fileInfo['name'], strlen($fileInfo['name'])-5, strlen($fileInfo['name']));
                $fileInfo['name'] = $file_name.$file_end;
            }
            // 获取默认的存储方式
            if ($config['default'] == 'aliyun') {
                //如果存储方式是阿里云,则获取视频的第N帧的截图
                $url='https://jcstatics.jiaqingfu.com.cn'.$save_dir . '/' . str_replace("\\","/", $fileName);
                $images=$url.'?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto';
            }
            // 4、写入数据库中
            $file = File::create([
                'cid'  => $cid,
                'type' => FileEnum::VIDEO_TYPE,
                'name' => $fileInfo['name'],
                'images' => $images??'',
                'uri'  => $save_dir . '/' . str_replace("\\","/", $fileName),
                'shop_id' => $shop_id,
                'user_id' => $user_id,
                'create_time' => time(),
            ]);
            // 5、返回结果
            return [
                'id'   => $file['id'],
                'cid'  => $file['cid'],
                'type' => $file['type'],
                'name' => $file['name'],
                'shop_id' => $file['shop_id'],
                'base_url' => $file['uri'],
                'uri'  => UrlServer::getFileUrl($file['uri'])
            ];

        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 音频上传
     */
    public static function audio($cid, $shop_id, $user_id=0,$save_dir='uploads/audio')
    {
        try {
            // 1、获取当前存储对象
            $config = [
                'default' => ConfigServer::get('storage', 'default', 'local'),
                'engine' => ConfigServer::get('storage_engine') ?? ['local'=>[]]
            ];

            // 2、执行文件上传
            $StorageDriver = new StorageDriver($config);
            $StorageDriver->setUploadFile('file');
            $fileName = $StorageDriver->getFileName();
            $fileInfo = $StorageDriver->getFileInfo();

            // 验证是否是音频
            // if (! check_is_audio($StorageDriver->getFileInfo()['realPath'] ?? '')) {
            //     throw new Exception('不是有效的音频文件');
            // }

            // 校验上传文件后缀
            if (!in_array(strtolower($fileInfo['ext']), config('project.file_audio'))) {
                throw new Exception("上传音频不允许上传". $fileInfo['ext'] . "文件");
            }

            // 上传文件
            $save_dir = $save_dir . '/' .  date('Ymd');
            if (!$StorageDriver->upload($save_dir)) {
                throw new Exception($StorageDriver->getError());
            }

            // 3、处理文件名称
            if (strlen($fileInfo['name']) > 128) {
                $file_name = substr($fileInfo['name'], 0, 123);
                $file_end = substr($fileInfo['name'], strlen($fileInfo['name'])-5, strlen($fileInfo['name']));
                $fileInfo['name'] = $file_name.$file_end;
            }

            // 4、写入数据库中
            $file = File::create([
                'cid'  => $cid,
                'type' => FileEnum::AUDIO_TYPE,
                'name' => $fileInfo['name'],
                'uri'  => $save_dir . '/' . str_replace("\\","/", $fileName),
                'shop_id' => $shop_id,
                'user_id' => $user_id,
                'create_time' => time(),
            ]);

            // 5、返回结果
            return [
                'id'   => $file['id'],
                'cid'  => $file['cid'],
                'type' => $file['type'],
                'name' => $file['name'],
                'shop_id' => $file['shop_id'],
                'uri'  => UrlServer::getFileUrl($file['uri']),
                'base_uri'  => $file['uri']
            ];

        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * Notes: 用户上传图片
     * @param string $save_dir (保存目录, 不建议修改, 不要超二级目录)
     * @param bool $isLocal (是否存不使用oss 只存本地, 上传退款证书会用到)
     * @return array
     * <AUTHOR> 9:53)
     */
    public static function other($save_dir='uploads/other', $isLocal=false )
    {
        try {
            if ($isLocal == false) {
                $config = [
                    'default' => ConfigServer::get('storage', 'default', 'local'),
                    'engine'  => ConfigServer::get('storage_engine')
                ];
            } else {
                $config = [
                    'default' => 'local',
                    'engine'  => ConfigServer::get('storage_engine')
                ];
            }
            if (empty($config['engine']['local'])) {
                $config['engine']['local'] = [];
            }

            // 确保目录存在
            $full_save_dir = public_path() . $save_dir;
            if (!is_dir($full_save_dir)) {
                mkdir($full_save_dir, 0755, true);
            }

            $StorageDriver = new StorageDriver($config);
            $StorageDriver->setUploadFile('file');

            if (!$StorageDriver->upload($save_dir)) {
                throw new Exception('上传失败：' . $StorageDriver->getError());
            }
            // 图片上传路径
            $fileName = $StorageDriver->getFileName();
            // 图片信息
            $fileInfo = $StorageDriver->getFileInfo();

            // 信息
            $data = [
                'name'        => $fileInfo['name'],
                'type'        => FileEnum::OTHER_TYPE,
                'uri'         => $save_dir . '/' . str_replace("\\","/", $fileName),
                'create_time' => time(),
                'size' => $fileInfo['size'],
                'shop_id' => 0
            ];
            $file = File::create($data);
            $data['id'] = $file['id'];
            return ['上传文件成功', $data];

        } catch (\Exception $e) {
            $message = lang($e->getMessage()) ?? $e->getMessage();
            return ['上传文件失败:',[$message]];
        }
    }



    /**
     * @notes 微信直播素材
     * @param $filePath
     * @return mixed
     * @throws Exception
     * @throws \GuzzleHttp\Exception\GuzzleException
     * <AUTHOR>
     * @date 2023/2/15 17:28\
     */
    public static function wechatLiveMaterial($filePath)
    {
        try {
            $fileName = basename($filePath);
            $config = WeChatServer::getMnpConfig();
            $app = Factory::miniProgram($config);
            $localFileDir = public_path() . pathinfo(parse_url($filePath, PHP_URL_PATH),  PATHINFO_DIRNAME) . '/';
            $localFile = $localFileDir . $fileName;

            // 切换oss后可能不存在，在本地查找后下载
            $config = [
                'default' => ConfigServer::get('storage', 'default', 'local'),
                'engine'  => ConfigServer::get('storage_engine')
            ];
            if ($config['default'] != 'local' && !file_exists($localFile)) {
                $res = download_file(UrlServer::getFileUrl($filePath), $localFileDir, $fileName);
                if (empty($res)) {
                    throw new Exception("资源下载失败");
                }
            }

            // 上传微信
            $result = $app->media->uploadImage($localFile);
            if (isset($result['errcode']) && 0 != $result['errcode']) {
                $err = $result['errmsg'] ?? '微信上传图片失败';
                throw new \Exception($err);
            }
            return $result['media_id'];
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 将base64编码转换为文件并保存（支持上传到OSS）
     * @param string $base64Data base64编码数据
     * @param string $save_dir 保存目录
     * @param string $file_name 文件名，默认为当前时间戳
     * @param bool $need_db 是否需要写入数据库
     * @param int $cid 分类ID
     * @param int $shop_id 商店ID
     * @param int $user_id 用户ID
     * @param bool $isLocal 是否只保存到本地，不上传到OSS
     * @return array|string 返回文件路径或完整信息
     * @throws Exception
     */
    public static function base64ToFile($base64Data, $save_dir = 'uploads/base64/', $file_name = '', $need_db = false, $cid = 0, $shop_id = 0, $user_id = 0, $isLocal = false)
    {
        try {
            // 处理base64数据
            if (preg_match('/^data:image\/(\w+);base64,/', $base64Data, $matches)) {
                $type = $matches[1]; // 获取图片类型
                $base64Data = substr($base64Data, strpos($base64Data, ',') + 1);
                $base64Data = str_replace(' ', '+', $base64Data);
            } else {
                // 如果不是标准的base64图片格式，尝试直接解码
                $type = 'png'; // 默认类型
            }

            // 生成文件名
            if (empty($file_name)) {
                $file_name = date('YmdHis') . substr(md5(uniqid()), 0, 5) . str_pad(rand(0, 9999), 4, '0', STR_PAD_LEFT) . '.' . $type;
            } elseif (!strpos($file_name, '.')) {
                $file_name .= '.' . $type;
            }

            // 确保目录存在
            $temp_dir = runtime_path() . 'temp/';
            if (!file_exists($temp_dir)) {
                mkdir($temp_dir, 0777, true);
            }

            // 先保存到临时文件
            $temp_file = $temp_dir . $file_name;
            $data = base64_decode($base64Data);
            if (file_put_contents($temp_file, $data) === false) {
                throw new Exception('保存临时文件失败');
            }

            // 获取存储配置
            if ($isLocal) {
                $config = [
                    'default' => 'local',
                    'engine'  => ConfigServer::get('storage_engine')
                ];
            } else {
                $config = [
                    'default' => ConfigServer::get('storage', 'default', 'local'),
                    'engine'  => ConfigServer::get('storage_engine') ?? ['local'=>[]]
                ];
            }

            // 使用存储驱动上传文件
            $StorageDriver = new StorageDriver($config);
            // 使用setUploadFileByReal方法，这个方法不需要realPath索引
            $StorageDriver->setUploadFileByReal($temp_file);

            // 确保目标目录存在（本地存储需要）
            if ($config['default'] == 'local' && !file_exists($save_dir)) {
                mkdir($save_dir, 0777, true);
            }

            // 上传文件
            if (!$StorageDriver->upload($save_dir)) {
                throw new Exception('上传文件失败：' . $StorageDriver->getError());
            }

            // 获取文件名和路径
            $fileName = $StorageDriver->getFileName();
            $file_path = $save_dir . '/' . str_replace("\\", "/", $fileName);

            // 删除临时文件
            if (file_exists($temp_file)) {
                unlink($temp_file);
            }

            // 是否需要写入数据库
            if ($need_db) {
                $file = File::create([
                    'cid'  => $cid,
                    'type' => FileEnum::IMAGE_TYPE,
                    'name' => pathinfo($fileName, PATHINFO_BASENAME),
                    'uri'  => $file_path,
                    'shop_id' => $shop_id,
                    'user_id' => $user_id,
                    'create_time' => time(),
                ]);

                return [
                    'id'   => $file['id'],
                    'cid'  => $file['cid'],
                    'type' => $file['type'],
                    'name' => $file['name'],
                    'shop_id' => $file['shop_id'],
                    'uri'  => UrlServer::getFileUrl($file['uri']),
                    'base_uri'  => $file['uri']
                ];
            }

            // 返回文件路径（如果是OSS，会通过UrlServer转换为完整URL）
            return $file_path;
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

}