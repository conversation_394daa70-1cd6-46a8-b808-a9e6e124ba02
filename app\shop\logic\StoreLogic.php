<?php


namespace app\shop\logic;


use app\common\basics\Logic;
use app\common\enum\ShopEnum;
use app\common\model\shop\Shop;
use app\common\model\shop\ShopDeposit;
use app\common\model\user\User;
use app\common\server\ConfigServer;
use app\common\server\UrlServer;

class StoreLogic extends Logic
{
    
    /**
     * @Notes: 获取商家详细
     * @Author: 张无忌
     * @param $shop_id
     * @return array
     */
    public static function detail($shop_id)
    {
        $model = new Shop();
        $detail = $model->field(true)
            ->with(['category'])
            ->json(['other_qualifications'],true)
            ->findOrEmpty($shop_id)->toArray();
        if (empty($detail)) {
          return false;
        }
        $detail['shop_label']=$detail['shop_label']??'';
        $detail['type']=$detail['type']??'';
        $detail['category'] = $detail['category']['name'] ?? '未知';
        $detail['shop_label'] = $detail['shop_label'] ? explode(',', $detail['shop_label']) : [];
        $detail['yan_level_STATUS'] = empty($detail['yan_level'])?'未检验':'已检验' ;
        $detail['f_user_status'] = empty($detail['f_user'])?'未分配':User::getUserInfo($detail['f_user']);
        $detail['type'] =$detail['type']? ShopEnum::getShopTypeDesc($detail['type']):'';
        $detail['run_start_time'] = $detail['run_start_time'] ? date('H:i:s', $detail['run_start_time']) : '';
        $detail['run_end_time'] = $detail['run_end_time'] ? date('H:i:s', $detail['run_end_time']) : '';
        $detail['business_license'] = $detail['business_license'] ? UrlServer::getFileUrl($detail['business_license']) : '';
        $detail['shop_doc'] = $detail['shop_doc'] ? UrlServer::getFileUrl($detail['shop_doc']) : '';
        if (!empty($detail['other_qualifications'])) {
            foreach ($detail['other_qualifications'] as &$val) {
                $val = UrlServer::getFileUrl($val);
            }
        }
        return $detail;
    }

    public static function lists($post,$shop_id){
        $get['page_no']= $post['page_no'] ?? 1;
        $get['limit']= $post['limit'] ?? 20;
        $model=new ShopDeposit();

        $where = ['shop_id'=>$shop_id];

        // 添加搜索条件
        if (!empty($post['payment_method'])) {
            $where['payment_method'] = $post['payment_method'];
        }
        if (isset($post['status']) && $post['status'] !== '') {
            $where['status'] = $post['status'];
        }

        $lists = $model->where($where)
            ->order('created_at desc')
            ->paginate([
                'page' => $get['page_no'],
                'list_rows' => $get['limit'],
                'var_page' => 'page'
            ])
            ->toArray();
     
        $status=['待审核','审核通过','审核不通过'];
        foreach ($lists['data'] as &$val) {
            $val['status_name'] = $status[$val['status']];

           
           

            // 处理支付方式显示
            if (empty($val['pay_status'])) {
                $val['payment_method'] = '未支付';
            }
        }

        return ['count' => $lists['total'], 'lists' => $lists['data']];
    }
    /**
     * @Notes: 修改商家信息
     * @Author: 张无忌
     * @param $post
     * @return bool
     */
    public static function edit($post)
    {
        try {
            $num = count($post['other_qualifications'] ?? []);
            if ($num > 5) {
                throw new \Exception('其他资质图片不能超过五张', 10006);
            }
            $post['shop_label']=$post['shop_label']??'';
            // 校验配送方式
            self::checkDeliveryType($post);
            if(!empty($post['shop_label'])){
                $shop_label=implode(',',$post['shop_label']);
            }else{
                $shop_label='';
            }
           
            Shop::update([
                'logo'       => $post['logo'],
                'nickname'       => $post['nickname'],
                'mobile'         => $post['mobile'],
                'keywords'       => $post['keywords'] ?? '',
                'intro'          => $post['intro'] ?? '',
                'videos'          => $post['videos'] ?? '',
                'is_run'         => $post['is_run'],
//                'service_mobile' => $post['service_mobile'],
                'weekdays'       => $post['weekdays'] ?? '',
                'province_id'    => $post['province_id'] ?? 0,
                'city_id'        => $post['city_id'] ?? 0,
                'district_id'    => $post['district_id'] ?? 0,
                'address'        => $post['address'] ?? '',
                'longitude'      => $post['longitude'] ?? '',
                'latitude'       => $post['latitude'] ?? '',
                'shop_label'       => $shop_label,
                'run_start_time' => empty($post['run_start_time']) ? '' : strtotime($post['run_start_time']),
                'run_end_time'   => empty($post['run_end_time']) ? '' : strtotime($post['run_end_time']),
                'refund_address' => json_encode([
                    'nickname'    => $post['refund_nickname'],
                    'mobile'      => $post['refund_mobile'],
                    'province_id' => $post['refund_province_id'],
                    'city_id'     => $post['refund_city_id'],
                    'district_id' => $post['refund_district_id'],
                    'address'     => $post['refund_address'],
                ], JSON_UNESCAPED_UNICODE),
                'business_license'   => empty($post['business_license']) ? '' : UrlServer::setFileUrl($post['business_license']),
                'shop_doc'           => empty($post['shop_doc']) ? '' : UrlServer::setFileUrl($post['shop_doc']),
                'other_qualifications' => isset($post['other_qualifications']) ? json_encode($post['other_qualifications'], JSON_UNESCAPED_UNICODE) : '',
                'open_invoice'  => $post['open_invoice'] ?? 0,
                'spec_invoice'  => $post['spec_invoice'] ?? 0,
                'delivery_type' => $post['delivery_type']
            ], ['id'=>$post['id']]);

            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 校验配送方式
     * @param $post
     * @return bool
     * @throws \Exception
     * <AUTHOR>
     * @date 2022/11/1 11:30
     */
    public static function checkDeliveryType($post)
    {
        // 校验配送方式
        if (empty($post['delivery_type'])) {
            throw new \Exception('至少选择一种配送方式');
        }

        // 线下自提时，商家地址必填
        if (in_array(ShopEnum::DELIVERY_SELF, $post['delivery_type'])) {
            if (empty($post['province_id']) || empty($post['city_id']) || empty($post['district_id']) || empty($post['address'])) {
                throw new \Exception('线下自提需完善商家地址');
            }
        }
        return true;
    }


    /*
     * 保存商家保证金协议
     */
    public static function saveAgree($post,$shop_id){
        //将多个文件路径用逗号隔开
        $shop_info=ShopDeposit::where('shop_id', $shop_id)->findOrEmpty()->toArray();

        if($shop_info){
           if($shop_info['pay_status']==1){
               $data['docs'] = implode(',', $post['filePaths']);
               ShopDeposit::update(['docs' => $data['docs'], 'status' => 0], ['id' => $shop_info->id]);
           }
        }else{
            $data['docs'] = implode(',', $post['filePaths']);
            $data['order_sn'] = createSn('shop_deposit','order_sn');
            $data['shop_id'] =$shop_id;
            $depositAmount=ConfigServer::get('shop_entry', 'depositAmount');
            $data['deposit_amount'] = $depositAmount;
            $data['payment_method'] = 'PC微信支付';
            $data['status'] = 0;
            return ShopDeposit::create($data);
        }





    }

    /**
     * 获取保证金状态详情
     */
    public static function getDepositStatus($id, $shop_id)
    {
        if($id==0){
            $detail = ShopDeposit::where('shop_id', $shop_id)
                ->order('id', 'desc')
                ->findOrEmpty()
                ->toArray();
        }else{

            $detail = ShopDeposit::where('id', $id)
                ->where('shop_id', $shop_id)
                ->findOrEmpty()
                ->toArray();

            if (empty($detail)) {
                return false;
            }

        }

        // 处理文档数组
        if (!empty($detail['docs'])) {
            $detail['docs_array'] = explode(',', $detail['docs']);
        } else {
            $detail['docs_array'] = [];
        }

        // 格式化时间
        if (!empty($detail['created_at'])) {
            $detail['create_time'] = $detail['created_at'];
        }
       
        if (!empty($detail['audit_time'])) {
            $detail['audit_time'] = date('Y-m-d H:i:s', $detail['audit_time']);
        }

        return $detail;
    }

}