<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片预览功能测试</title>
    <link rel="stylesheet" href="/static/lib/layui/css/layui.css">
    <link rel="stylesheet" href="/static/admin/css/like.css">
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="test-section">
        <div class="test-title">图片预览功能测试</div>
        <div class="test-description">点击下面的图片应该能够放大查看</div>
        
        <!-- 模拟商品图片上传后的结构 -->
        <div class="like-upload-image">
            <div class="upload-image-div">
                <img src="https://picsum.photos/200/200?random=1" alt="测试图片1" class="preview-image" data-src="https://picsum.photos/800/800?random=1" title="点击放大查看">
                <div class="del-upload-btn">x</div>
            </div>
            <div class="upload-image-div">
                <img src="https://picsum.photos/200/200?random=2" alt="测试图片2" class="preview-image" data-src="https://picsum.photos/800/800?random=2" title="点击放大查看">
                <div class="del-upload-btn">x</div>
            </div>
            <div class="upload-image-div">
                <img src="https://picsum.photos/200/200?random=3" alt="测试图片3" class="preview-image" data-src="https://picsum.photos/800/800?random=3" title="点击放大查看">
                <div class="del-upload-btn">x</div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">表格中的图片测试</div>
        <div class="test-description">表格中的图片也应该支持点击放大</div>
        
        <table class="layui-table">
            <thead>
                <tr>
                    <th>商品名称</th>
                    <th>商品图片</th>
                    <th>价格</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>测试商品1</td>
                    <td><img src="https://picsum.photos/60/60?random=4" alt="商品图片" class="image-show" style="width: 60px; height: 60px; border-radius: 4px;"></td>
                    <td>¥99.00</td>
                </tr>
                <tr>
                    <td>测试商品2</td>
                    <td><img src="https://picsum.photos/60/60?random=5" alt="商品图片" class="image-show" style="width: 60px; height: 60px; border-radius: 4px;"></td>
                    <td>¥199.00</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <div class="test-title">动态添加图片测试</div>
        <div class="test-description">点击按钮动态添加图片，新添加的图片也应该支持预览</div>
        
        <button id="addImageBtn" class="layui-btn layui-btn-normal">添加图片</button>
        <div id="dynamicImages" class="like-upload-image" style="margin-top: 15px;">
            <!-- 动态添加的图片会出现在这里 -->
        </div>
    </div>

    <script src="/static/lib/layui/layui.js"></script>
    <script src="/static/admin/js/jquery.min.js"></script>
    <script src="/static/admin/js/function.js"></script>
    <script src="/static/common/js/image-preview.js"></script>
    
    <script>
        layui.use(['layer'], function() {
            var layer = layui.layer;
            
            // 动态添加图片测试
            $('#addImageBtn').click(function() {
                var randomId = Math.floor(Math.random() * 1000);
                var imageHtml = '<div class="upload-image-div">' +
                    '<img src="https://picsum.photos/200/200?random=' + randomId + '" alt="动态图片" class="preview-image" data-src="https://picsum.photos/800/800?random=' + randomId + '" title="点击放大查看">' +
                    '<div class="del-upload-btn">x</div>' +
                    '</div>';
                
                $('#dynamicImages').append(imageHtml);
                layer.msg('图片已添加，点击可预览', {icon: 1, time: 1500});
            });
            
            // 删除图片功能
            $(document).on('click', '.del-upload-btn', function(e) {
                e.stopPropagation();
                var $parent = $(this).parent();
                layer.confirm('确定要删除这张图片吗？', {icon: 3, title:'提示'}, function(index){
                    $parent.remove();
                    layer.close(index);
                    layer.msg('已删除', {icon: 1, time: 1000});
                });
            });
        });
    </script>
</body>
</html>
