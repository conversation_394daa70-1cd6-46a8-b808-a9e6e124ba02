-- ----------------------------
-- Table structure for ls_shop_password_reset
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_password_reset`;
CREATE TABLE `ls_shop_password_reset` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '商家管理员ID',
  `shop_id` int(11) NOT NULL COMMENT '商家ID',
  `mobile` varchar(20) NOT NULL COMMENT '手机号',
  `code` varchar(6) NOT NULL COMMENT '验证码',
  `token` varchar(64) NOT NULL COMMENT '重置令牌',
  `expire_time` int(11) NOT NULL COMMENT '过期时间',
  `used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用(0=未使用, 1=已使用)',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_admin_id` (`admin_id`) USING BTREE,
  KEY `idx_mobile` (`mobile`) USING BTREE,
  KEY `idx_token` (`token`) USING BTREE,
  KEY `idx_expire_time` (`expire_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家密码重置表';
