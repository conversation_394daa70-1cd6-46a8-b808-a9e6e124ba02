<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use app\common\server\WeChatServer;
use EasyWeChat\Factory;

/**
 * 微信公众号关注用户同步命令
 * Class WechatFollowers
 * @package app\command
 */
class WechatFollowers extends Command
{
    protected function configure()
    {
        $this->setName('wechat:followers')
            ->setDescription('同步微信公众号关注用户列表');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始同步微信公众号关注用户列表...');
        
        try {
            // 获取微信公众号配置
            $config = WeChatServer::getOaConfig();
            $app = Factory::officialAccount($config);
            
            $nextOpenId = null;
            $totalCount = 0;
            $newCount = 0;
            
            do {
                // 获取用户列表
                $result = $app->user->list($nextOpenId);
                
                if (!isset($result['data']['openid']) || empty($result['data']['openid'])) {
                    break;
                }
                
                $openIds = $result['data']['openid'];
                $totalCount += count($openIds);
                
                // 批量获取用户详细信息
                $userInfos = $app->user->select($openIds);
                
                if (isset($userInfos['user_info_list'])) {
                    foreach ($userInfos['user_info_list'] as $userInfo) {
                        $newCount += $this->saveFollower($userInfo);
                    }
                }
                
                // 设置下一次请求的起始openid
                $nextOpenId = $result['next_openid'] ?? null;
                
                $output->writeln("已处理 {$totalCount} 个用户");
                
            } while (!empty($nextOpenId));
            
            $output->writeln("同步完成！总计: {$totalCount} 个用户，新增: {$newCount} 个用户");
            
        } catch (\Exception $e) {
            $output->writeln("同步失败：" . $e->getMessage());
            
            // 记录错误日志
            Db::name('log')->insert([
                'log' => '微信关注用户同步失败：' . $e->getMessage(),
                'type' => 'wechat_followers_sync',
                'create_time' => time()
            ]);
        }
    }
    
    /**
     * 保存关注用户信息
     * @param array $userInfo
     * @return int 1=新增，0=已存在
     */
    private function saveFollower($userInfo)
    {
        // 检查用户是否已存在
        $exists = Db::name('wechat_followers')
            ->where('openid', $userInfo['openid'])
            ->find();
            
        $data = [
            'openid' => $userInfo['openid'],
            'unionid' => $userInfo['unionid'] ?? '',
            'nickname' => $userInfo['nickname'] ?? '',
            'sex' => $userInfo['sex'] ?? 0,
            'city' => $userInfo['city'] ?? '',
            'country' => $userInfo['country'] ?? '',
            'province' => $userInfo['province'] ?? '',
            'language' => $userInfo['language'] ?? '',
            'headimgurl' => $userInfo['headimgurl'] ?? '',
            'subscribe_time' => $userInfo['subscribe_time'] ?? 0,
            'subscribe' => $userInfo['subscribe'] ?? 1,
            'update_time' => time()
        ];
        
        if ($exists) {
            // 更新现有记录
            Db::name('wechat_followers')
                ->where('id', $exists['id'])
                ->update($data);
            return 0;
        } else {
            // 插入新记录
            $data['create_time'] = time();
            Db::name('wechat_followers')->insert($data);
            return 1;
        }
    }
}
