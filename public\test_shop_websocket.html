<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家 WebSocket 通知测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.binding {
            background-color: #cce5ff;
            color: #004085;
        }
        .controls {
            margin: 20px 0;
        }
        .controls input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .controls button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        .controls button:hover {
            background-color: #0056b3;
        }
        .controls button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .messages {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            margin-top: 20px;
        }
        .message {
            padding: 5px;
            margin: 5px 0;
            border-left: 3px solid #007bff;
            background-color: white;
            font-size: 14px;
        }
        .message.notification {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .message.error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .clear-btn {
            background-color: #6c757d;
        }
        .info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>商家 WebSocket 通知测试</h1>
        
        <div class="info">
            <strong>说明：</strong>本页面用于测试商家 WebSocket 连接和通知接收功能。
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="controls">
            <input type="number" id="shopId" placeholder="输入商家ID" value="">
            <button id="connectBtn" onclick="connect()">连接 WebSocket</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
            <button id="bindBtn" onclick="bindShop()" disabled>绑定商家</button>
            <button class="clear-btn" onclick="clearMessages()">清空消息</button>
        </div>
        
        <div id="messages" class="messages"></div>
    </div>

    <script>
        let ws = null;
        let heartbeatInterval = null;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 10;
        const reconnectDelay = 3000;
        
        // WebSocket 配置
        const wsConfig = {
            url: 'wss://kefu.huohanghang.cn:9282',
            backupUrls: [
                'wss://www.huohanghang.cn:9282',
                'wss://127.0.0.1:9282',
                'wss://localhost:9282'
            ]
        };

        function log(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            const time = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `[${time}] ${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateStatus(status, text) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = text;
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('已经连接，请先断开连接', 'error');
                return;
            }

            const shopId = document.getElementById('shopId').value;
            if (!shopId) {
                log('请输入商家ID', 'error');
                return;
            }

            updateStatus('binding', '正在连接...');
            log(`尝试连接到 ${wsConfig.url}`);

            try {
                ws = new WebSocket(wsConfig.url);

                ws.onopen = function() {
                    updateStatus('connected', '已连接');
                    log('WebSocket 连接成功', 'success');
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    document.getElementById('bindBtn').disabled = false;
                    reconnectAttempts = 0;
                    
                    // 启动心跳
                    startHeartbeat();
                    
                    // 自动绑定商家
                    setTimeout(() => bindShop(), 500);
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        
                        switch (data.type) {
                            case 'connect':
                                log(`服务器连接确认: ${data.message}, client_id: ${data.client_id}`);
                                break;
                                
                            case 'bind_success':
                                log(`商家绑定成功: shop_id = ${data.shop_id}`, 'success');
                                break;
                                
                            case 'notification':
                                log(`收到通知: ${data.data.title} - ${data.data.content}`, 'notification');
                                // 显示通知详情
                                log(`通知详情: ${JSON.stringify(data.data, null, 2)}`, 'notification');
                                // 播放通知声音（如果需要）
                                playNotificationSound();
                                break;
                                
                            case 'heartbeat':
                                // 心跳响应，不显示在界面上
                                console.log('Heartbeat response received');
                                break;
                                
                            case 'error':
                                log(`错误: ${data.message}`, 'error');
                                break;
                                
                            default:
                                log(`收到消息: ${JSON.stringify(data)}`);
                        }
                    } catch (e) {
                        log(`解析消息失败: ${event.data}`, 'error');
                    }
                };

                ws.onerror = function(error) {
                    log(`WebSocket 错误: ${error}`, 'error');
                    updateStatus('disconnected', '连接错误');
                };

                ws.onclose = function() {
                    updateStatus('disconnected', '连接已断开');
                    log('WebSocket 连接已关闭');
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    document.getElementById('bindBtn').disabled = true;
                    stopHeartbeat();
                    
                    // 尝试重连
                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        log(`将在 ${reconnectDelay/1000} 秒后尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`);
                        setTimeout(() => connect(), reconnectDelay);
                    }
                };

            } catch (e) {
                log(`创建 WebSocket 连接失败: ${e.message}`, 'error');
                updateStatus('disconnected', '连接失败');
            }
        }

        function disconnect() {
            if (ws) {
                reconnectAttempts = maxReconnectAttempts; // 防止自动重连
                ws.close();
                ws = null;
            }
        }

        function bindShop() {
            const shopId = document.getElementById('shopId').value;
            if (!shopId) {
                log('请输入商家ID', 'error');
                return;
            }

            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket 未连接', 'error');
                return;
            }

            const message = {
                type: 'shop_online',
                shop_id: shopId
            };

            ws.send(JSON.stringify(message));
            log(`发送商家绑定请求: shop_id = ${shopId}`);
        }

        function startHeartbeat() {
            heartbeatInterval = setInterval(() => {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({ type: 'heartbeat' }));
                    console.log('Heartbeat sent');
                }
            }, 30000); // 每30秒发送一次心跳
        }

        function stopHeartbeat() {
            if (heartbeatInterval) {
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
            log('消息已清空');
        }

        function playNotificationSound() {
            // 可选：播放通知声音
            try {
                const audio = new Audio('https://jcstatics.jiaqingfu.com.cnuploads/audio/tomsg.mp3');
                audio.play().catch(e => console.log('播放声音失败:', e));
            } catch (e) {
                console.log('创建音频对象失败:', e);
            }
        }

        // 页面加载时自动获取商家ID（如果有的话）
        window.onload = function() {
            // 尝试从 URL 参数获取商家ID
            const urlParams = new URLSearchParams(window.location.search);
            const shopId = urlParams.get('shop_id');
            if (shopId) {
                document.getElementById('shopId').value = shopId;
                log(`从 URL 参数获取商家ID: ${shopId}`);
            }
        };

        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            if (ws) {
                ws.close();
            }
        };
    </script>
</body>
</html>
