<?php
// +----------------------------------------------------------------------
// | WebSocket Gateway 配置
// +----------------------------------------------------------------------

return [
    // 注册服务地址，用于服务端内部通信
    'register_address' => '127.0.0.1:1236',

    // 客户端连接地址，用于前端 WebSocket 连接
    'client_address' => 'wss://kefu.huohanghang.cn',

    // 备用客户端连接地址，当主地址连接失败时使用
    'backup_client_addresses' => [
        'wss://www.huohanghang.cn',
        'wss://127.0.0.1:9282',
        'wss://localhost:9282',
        'wss://127.0.0.1:9272',
        'wss://127.0.0.1:9283',
    ],

    // 心跳间隔（秒）
    'heartbeat_interval' => 55,

    // 重连间隔（秒）
    'reconnect_interval' => 3,

    // 最大重连次数
    'max_reconnect_attempts' => 10,

    // 用户组配置
    'groups' => [
        'admin_group' => '管理员组',
        'shop_group' => '商家组',
        'user_group' => '用户组',
    ],

    // 通知声音文件路径
    'notification_sound' => 'https://jcstatics.jiaqingfu.com.cnuploads/audio/tomsg.mp3',

    // 是否强制使用WS协议（不使用WSS）- 设置为false，根据页面协议选择
    'force_ws' => false, // 根据页面协议选择WebSocket协议

    // WSS配置
    'ssl_cert_file' => __DIR__ . '/../cert/ssl.pem', // SSL证书路径
    'ssl_key_file' => __DIR__ . '/../cert/ssl.key',  // SSL密钥路径

    // 是否允许跨域连接
    'allow_cross_domain' => true,

    // 调试模式
    'debug' => true,
];
