<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\api\logic\WechatFollowLogic;
use app\common\server\JsonServer;

/**
 * 微信关注检查控制器
 * Class WechatFollow
 * @package app\api\controller
 */
class WechatFollow extends Api
{
    public $like_not_need_login = [];

    /**
     * 检查用户是否关注公众号
     * @return \think\response\Json
     */
    public function checkFollow()
    {
        $result = WechatFollowLogic::checkUserFollow($this->user_id);
        
        if ($result['is_follow']) {
            return JsonServer::success('用户已关注公众号', $result);
        } else {
            return JsonServer::success('用户未关注公众号', $result);
        }
    }

    /**
     * 获取关注引导信息
     * @return \think\response\Json
     */
    public function getFollowGuide()
    {
        $result = WechatFollowLogic::getFollowGuide();
        return JsonServer::success('', $result);
    }

    /**
     * 记录用户关注引导展示
     * @return \think\response\Json
     */
    public function recordGuideShow()
    {
        $result = WechatFollowLogic::recordGuideShow($this->user_id);
        return JsonServer::success('记录成功', $result);
    }
}
